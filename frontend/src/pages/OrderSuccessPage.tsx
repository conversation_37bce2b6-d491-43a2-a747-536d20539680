import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { CheckCircleIcon, PhoneIcon, ScaleIcon } from '@heroicons/react/24/outline';

interface LocationState {
  orderNumber?: string;
  totalWeight?: number;
}

const OrderSuccessPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const state = location.state as LocationState;

  const goBackToCollection = () => {
    const accessInfo = sessionStorage.getItem('collection-access');
    if (accessInfo) {
      try {
        const parsed = JSON.parse(accessInfo);
        navigate(`/collection/${parsed.collectionId}`);
      } catch (error) {
        navigate('/');
      }
    } else {
      navigate('/');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <CheckCircleIcon className="mx-auto h-16 w-16 text-green-500" />
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Order Submitted!
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Your order has been successfully submitted
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 space-y-4">
          {state?.orderNumber && (
            <div className="text-center">
              <p className="text-sm text-gray-600">Order Number</p>
              <p className="text-lg font-semibold text-gray-900">{state.orderNumber}</p>
            </div>
          )}

          {state?.totalWeight && (
            <div className="flex items-center justify-center space-x-2 text-blue-600">
              <ScaleIcon className="h-5 w-5" />
              <span className="font-medium">{state.totalWeight.toFixed(1)}g total weight</span>
            </div>
          )}

          <div className="border-t pt-4">
            <div className="flex items-center justify-center space-x-2 text-gray-600 mb-4">
              <PhoneIcon className="h-5 w-5" />
              <span className="text-sm">What happens next?</span>
            </div>
            <ul className="text-sm text-gray-600 space-y-2">
              <li className="flex items-start">
                <span className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"></span>
                <span>You will receive a phone call to confirm your order</span>
              </li>
              <li className="flex items-start">
                <span className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"></span>
                <span>Order details and pickup/delivery will be arranged</span>
              </li>
              <li className="flex items-start">
                <span className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"></span>
                <span>No payment is required at this time</span>
              </li>
              <li className="flex items-start">
                <span className="flex-shrink-0 w-2 h-2 bg-red-500 rounded-full mt-2 mr-3"></span>
                <span>This collection link has been deactivated</span>
              </li>
            </ul>
          </div>
        </div>

        <div className="text-center space-y-4">
          <button
            onClick={goBackToCollection}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 font-medium"
          >
            Continue Shopping
          </button>
          
          <p className="text-xs text-gray-500">
            Thank you for your order! We'll be in touch soon.
          </p>
        </div>
      </div>
    </div>
  );
};

export default OrderSuccessPage;
