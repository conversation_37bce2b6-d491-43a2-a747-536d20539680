import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { LockClosedIcon, PhoneIcon, KeyIcon } from '@heroicons/react/24/outline';
import { getCollectionByShareURL, validateCollectionAccess } from '../lib/api';
import { useNotification } from '../contexts/NotificationContext';

interface AccessFormData {
  phone: string;
  accessCode: string;
}

const CollectionAccessPage: React.FC = () => {
  const { shareUrlId } = useParams<{ shareUrlId: string }>();
  const navigate = useNavigate();
  const { addNotification } = useNotification();
  const [formData, setFormData] = useState<AccessFormData>({
    phone: '',
    accessCode: ''
  });
  const [isValidating, setIsValidating] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Get collection info by share URL
  const { data: accessInfo, isLoading, error } = useQuery({
    queryKey: ['collection-access', shareUrlId],
    queryFn: () => getCollectionByShareURL(shareUrlId!),
    enabled: !!shareUrlId,
    retry: false,
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.phone.trim() || !formData.accessCode.trim()) {
      addNotification('error', 'Please enter both phone number and access code');
      return;
    }

    if (formData.accessCode.length !== 4 || !/^\d{4}$/.test(formData.accessCode)) {
      addNotification('error', 'Access code must be 4 digits');
      return;
    }

    setIsValidating(true);

    try {
      const collection = await validateCollectionAccess(shareUrlId!, formData.accessCode);
      
      // Store authentication info in sessionStorage
      sessionStorage.setItem('collection-access', JSON.stringify({
        shareUrlId,
        phone: formData.phone,
        accessCode: formData.accessCode,
        collectionId: collection.id,
        authenticated: true
      }));

      setIsAuthenticated(true);
      addNotification('success', 'Access granted! Redirecting to collection...');
      
      // Redirect to collection view
      setTimeout(() => {
        navigate(`/collection/${collection.id}`, { replace: true });
      }, 1000);

    } catch (error: any) {
      console.error('Access validation failed:', error);
      if (error.response?.status === 401) {
        addNotification('error', 'Invalid access code. Please try again.');
      } else if (error.response?.status === 404) {
        addNotification('error', 'Collection not found or access expired.');
      } else {
        addNotification('error', 'Failed to validate access. Please try again.');
      }
    } finally {
      setIsValidating(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading collection access...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
          <div className="text-red-500 mb-4">
            <LockClosedIcon className="h-16 w-16 mx-auto" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Not Found</h1>
          <p className="text-gray-600 mb-6">
            The collection access link is invalid or has expired.
          </p>
          <p className="text-sm text-gray-500">
            Please contact the person who shared this link with you.
          </p>
        </div>
      </div>
    );
  }

  if (isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
          <div className="text-green-500 mb-4">
            <svg className="h-16 w-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Granted!</h1>
          <p className="text-gray-600">Redirecting to collection...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <LockClosedIcon className="mx-auto h-12 w-12 text-blue-600" />
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Collection Access
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Enter your phone number and access code to view the collection
          </p>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
                Phone Number
              </label>
              <div className="mt-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <PhoneIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="phone"
                  name="phone"
                  type="tel"
                  required
                  value={formData.phone}
                  onChange={handleInputChange}
                  className="appearance-none relative block w-full pl-10 pr-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                  placeholder="Enter your phone number"
                />
              </div>
            </div>

            <div>
              <label htmlFor="accessCode" className="block text-sm font-medium text-gray-700">
                Access Code
              </label>
              <div className="mt-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <KeyIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="accessCode"
                  name="accessCode"
                  type="text"
                  required
                  maxLength={4}
                  pattern="[0-9]{4}"
                  value={formData.accessCode}
                  onChange={handleInputChange}
                  className="appearance-none relative block w-full pl-10 pr-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm text-center text-lg tracking-widest"
                  placeholder="0000"
                />
              </div>
              <p className="mt-1 text-xs text-gray-500">
                Enter the 4-digit code provided to you
              </p>
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={isValidating}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isValidating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Validating...
                </>
              ) : (
                'Access Collection'
              )}
            </button>
          </div>
        </form>

        <div className="text-center">
          <p className="text-xs text-gray-500">
            Don't have an access code? Contact the person who shared this link with you.
          </p>
        </div>
      </div>
    </div>
  );
};

export default CollectionAccessPage;
