import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { ShoppingBagIcon, ScaleIcon } from '@heroicons/react/24/outline';
import { getCollectionWithProducts } from '../lib/api';
import { useCart } from '../contexts/CartContext';
import { useNotification } from '../contexts/NotificationContext';
import type { Product } from '../types';

const SimpleCollectionPage: React.FC = () => {
  const { collectionId } = useParams<{ collectionId: string }>();
  const navigate = useNavigate();
  const { addItem, isInCart, getItemQuantity } = useCart();
  const { addNotification } = useNotification();

  // Check authentication
  useEffect(() => {
    const accessInfo = sessionStorage.getItem('collection-access');
    if (!accessInfo) {
      navigate('/', { replace: true });
      return;
    }

    try {
      const parsed = JSON.parse(accessInfo);
      if (!parsed.authenticated || parsed.collectionId !== collectionId) {
        navigate('/', { replace: true });
      }
    } catch (error) {
      navigate('/', { replace: true });
    }
  }, [collectionId, navigate]);

  // Get collection with products
  const { data: collection, isLoading, error } = useQuery({
    queryKey: ['collection-products', collectionId],
    queryFn: () => getCollectionWithProducts(collectionId!),
    enabled: !!collectionId,
  });

  const handleReserveProduct = (product: Product) => {
    addItem(product, 1);
    addNotification('success', `${product.name} added to cart`);
  };

  const getImageUrl = (product: Product) => {
    if (product.primary_image?.medium_url) {
      return product.primary_image.medium_url;
    }
    if (product.images && product.images.length > 0) {
      return product.images[0].medium_url || product.images[0].image_url;
    }
    return '/placeholder-jewelry.jpg';
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading collection...</p>
        </div>
      </div>
    );
  }

  if (error || !collection) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Collection Not Found</h1>
          <p className="text-gray-600 mb-6">
            The collection could not be loaded. Please try again.
          </p>
          <button
            onClick={() => navigate('/')}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{collection.name}</h1>
              {collection.description && (
                <p className="mt-2 text-gray-600">{collection.description}</p>
              )}
            </div>
            <button
              onClick={() => navigate('/cart')}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2"
            >
              <ShoppingBagIcon className="h-5 w-5" />
              <span>View Cart</span>
            </button>
          </div>
        </div>
      </div>

      {/* Products Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {!collection.products || collection.products.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">No products in this collection</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {collection.products.map((product) => (
              <div
                key={product.id}
                className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
              >
                {/* Product Image */}
                <div className="aspect-square bg-gray-100">
                  <img
                    src={getImageUrl(product)}
                    alt={product.name}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = '/placeholder-jewelry.jpg';
                    }}
                  />
                </div>

                {/* Product Info */}
                <div className="p-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {product.name}
                  </h3>
                  
                  {product.sku && (
                    <p className="text-sm text-gray-500 mb-2">SKU: {product.sku}</p>
                  )}

                  {product.weight && (
                    <div className="flex items-center text-sm text-gray-600 mb-3">
                      <ScaleIcon className="h-4 w-4 mr-1" />
                      <span>{product.weight}g</span>
                    </div>
                  )}

                  {product.description && (
                    <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                      {product.description}
                    </p>
                  )}

                  {/* Product Details */}
                  <div className="space-y-1 mb-4">
                    {product.category && (
                      <p className="text-xs text-gray-500">
                        <span className="font-medium">Category:</span> {product.category}
                      </p>
                    )}
                    {product.material && (
                      <p className="text-xs text-gray-500">
                        <span className="font-medium">Material:</span> {product.material}
                      </p>
                    )}
                    {product.metal_purity && (
                      <p className="text-xs text-gray-500">
                        <span className="font-medium">Purity:</span> {product.metal_purity}
                      </p>
                    )}
                  </div>

                  {/* Reserve Button */}
                  <div className="flex items-center justify-between">
                    {isInCart(product.id) ? (
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-green-600 font-medium">
                          In Cart ({getItemQuantity(product.id)})
                        </span>
                        <button
                          onClick={() => handleReserveProduct(product)}
                          className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                        >
                          Add More
                        </button>
                      </div>
                    ) : (
                      <button
                        onClick={() => handleReserveProduct(product)}
                        disabled={product.availability !== 'available'}
                        className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                      >
                        <ShoppingBagIcon className="h-4 w-4" />
                        <span>
                          {product.availability === 'available' ? 'Reserve' : 'Unavailable'}
                        </span>
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default SimpleCollectionPage;
