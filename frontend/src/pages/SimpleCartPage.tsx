import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { TrashIcon, MinusIcon, PlusIcon, ScaleIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';
import { useCart } from '../contexts/CartContext';
import { useNotification } from '../contexts/NotificationContext';
import { createOrder, deactivateCollectionAccess } from '../lib/api';

const SimpleCartPage: React.FC = () => {
  const navigate = useNavigate();
  const { cart, removeItem, updateQuantity, clearCart } = useCart();
  const { addNotification } = useNotification();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const getImageUrl = (product: any) => {
    if (product.primary_image?.thumbnail_url) {
      return product.primary_image.thumbnail_url;
    }
    if (product.images && product.images.length > 0) {
      return product.images[0].thumbnail_url || product.images[0].image_url;
    }
    return '/placeholder-jewelry.jpg';
  };

  const handleQuantityChange = (productId: string, newQuantity: number) => {
    if (newQuantity < 1) {
      removeItem(productId);
    } else {
      updateQuantity(productId, newQuantity);
    }
  };

  const handleSubmitOrder = async () => {
    if (cart.items.length === 0) {
      addNotification('error', 'Your cart is empty');
      return;
    }

    // Get authentication info
    const accessInfo = sessionStorage.getItem('collection-access');
    if (!accessInfo) {
      addNotification('error', 'Authentication required');
      navigate('/');
      return;
    }

    let parsedAccessInfo;
    try {
      parsedAccessInfo = JSON.parse(accessInfo);
    } catch (error) {
      addNotification('error', 'Invalid authentication');
      navigate('/');
      return;
    }

    setIsSubmitting(true);

    try {
      const orderData = {
        customer_id: parsedAccessInfo.customerId,
        items: cart.items.map(item => ({
          product_id: item.product.id,
          quantity: item.quantity,
        })),
        delivery_address: {
          street: "To be confirmed by phone",
          city: "To be confirmed",
          state: "To be confirmed",
          country: "India"
        },
        discount_amount: 0,
        notes: `Order placed via collection access. Phone: ${parsedAccessInfo.phone}. Access code: ${parsedAccessInfo.accessCode}. Total weight: ${cart.total_weight}g`,
      };

      const order = await createOrder(orderData);

      // Deactivate the collection access link after successful order
      try {
        await deactivateCollectionAccess(parsedAccessInfo.collectionId, parsedAccessInfo.accessId);
      } catch (deactivateError) {
        console.warn('Failed to deactivate collection access:', deactivateError);
        // Don't fail the order if deactivation fails
      }

      addNotification('success', 'Order submitted successfully!');
      clearCart();

      // Show success message and redirect
      setTimeout(() => {
        navigate('/order-success', {
          state: {
            orderNumber: order.order_number,
            totalWeight: cart.total_weight
          }
        });
      }, 1000);

    } catch (error: any) {
      console.error('Order submission failed:', error);
      addNotification('error', 'Failed to submit order. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const goBackToCollection = () => {
    const accessInfo = sessionStorage.getItem('collection-access');
    if (accessInfo) {
      try {
        const parsed = JSON.parse(accessInfo);
        navigate(`/collection/${parsed.collectionId}`);
      } catch (error) {
        navigate('/');
      }
    } else {
      navigate('/');
    }
  };

  if (cart.items.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <svg className="h-24 w-24 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z" />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Your cart is empty</h2>
            <p className="text-gray-600 mb-8">Add some items to your cart to get started</p>
            <button
              onClick={goBackToCollection}
              className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 flex items-center space-x-2 mx-auto"
            >
              <ArrowLeftIcon className="h-5 w-5" />
              <span>Continue Shopping</span>
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Your Cart</h1>
            <p className="text-gray-600 mt-1">{cart.total_items} item{cart.total_items !== 1 ? 's' : ''} selected</p>
          </div>
          <button
            onClick={goBackToCollection}
            className="text-blue-600 hover:text-blue-700 flex items-center space-x-2"
          >
            <ArrowLeftIcon className="h-5 w-5" />
            <span>Continue Shopping</span>
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-md">
              <div className="p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Selected Items</h2>
                <div className="space-y-4">
                  {cart.items.map((item) => (
                    <div key={item.product.id} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
                      {/* Product Image */}
                      <div className="flex-shrink-0">
                        <img
                          src={getImageUrl(item.product)}
                          alt={item.product.name}
                          className="w-16 h-16 object-cover rounded-md"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.src = '/placeholder-jewelry.jpg';
                          }}
                        />
                      </div>

                      {/* Product Info */}
                      <div className="flex-1 min-w-0">
                        <h3 className="text-sm font-medium text-gray-900">{item.product.name}</h3>
                        {item.product.sku && (
                          <p className="text-xs text-gray-500">SKU: {item.product.sku}</p>
                        )}
                        {item.product.weight && (
                          <div className="flex items-center text-xs text-gray-600 mt-1">
                            <ScaleIcon className="h-3 w-3 mr-1" />
                            <span>{item.product.weight}g each</span>
                          </div>
                        )}
                      </div>

                      {/* Quantity Controls */}
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleQuantityChange(item.product.id, item.quantity - 1)}
                          className="p-1 text-gray-400 hover:text-gray-600"
                        >
                          <MinusIcon className="h-4 w-4" />
                        </button>
                        <span className="text-sm font-medium text-gray-900 w-8 text-center">
                          {item.quantity}
                        </span>
                        <button
                          onClick={() => handleQuantityChange(item.product.id, item.quantity + 1)}
                          className="p-1 text-gray-400 hover:text-gray-600"
                        >
                          <PlusIcon className="h-4 w-4" />
                        </button>
                      </div>

                      {/* Weight */}
                      <div className="text-sm text-gray-600 min-w-0">
                        {item.product.weight ? `${(item.product.weight * item.quantity).toFixed(1)}g` : 'N/A'}
                      </div>

                      {/* Remove Button */}
                      <button
                        onClick={() => removeItem(item.product.id)}
                        className="p-1 text-red-400 hover:text-red-600"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-6 sticky top-8">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h2>
              
              <div className="space-y-3 mb-6">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Total Items:</span>
                  <span className="font-medium">{cart.total_items}</span>
                </div>
                <div className="flex justify-between text-lg font-semibold border-t pt-3">
                  <span className="text-gray-900 flex items-center">
                    <ScaleIcon className="h-5 w-5 mr-1" />
                    Total Weight:
                  </span>
                  <span className="text-blue-600">{cart.total_weight.toFixed(1)}g</span>
                </div>
              </div>

              <button
                onClick={handleSubmitOrder}
                disabled={isSubmitting}
                className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white inline-block mr-2"></div>
                    Submitting Order...
                  </>
                ) : (
                  'Submit Order'
                )}
              </button>

              <p className="text-xs text-gray-500 mt-3 text-center">
                Your order will be confirmed by phone call
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleCartPage;
