import axios from 'axios';
import type {
  Product,
  Collection,
  Order,
  Customer,
  PaginatedResponse,
  FilterOptions,
  CustomerFormData,
} from '../types';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:8080/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API Response Error:', error);
    
    if (error.response?.status === 404) {
      throw new Error('Resource not found');
    } else if (error.response?.status === 500) {
      throw new Error('Server error. Please try again later.');
    } else if (error.code === 'ECONNABORTED') {
      throw new Error('Request timeout. Please check your connection.');
    }
    
    throw error;
  }
);

// Collections API
export const getCollections = async (filters: FilterOptions = {}): Promise<PaginatedResponse<Collection>> => {
  const params = new URLSearchParams();
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== '') {
      params.append(key, String(value));
    }
  });

  const response = await api.get(`/collections?${params}`);
  return {
    data: response.data.collections || [],
    total: response.data.total || 0,
    page: filters.page || 1,
    limit: filters.limit || 20,
    total_pages: Math.ceil((response.data.total || 0) / (filters.limit || 20)),
  };
};

export const getCollection = async (slug: string): Promise<Collection> => {
  const response = await api.get(`/collections/slug/${slug}`);
  return response.data;
};

export const getCollectionWithProducts = async (collectionId: string): Promise<Collection> => {
  const response = await api.get(`/collections/${collectionId}/products`);
  return response.data;
};

// Products API
export const getProducts = async (filters: FilterOptions = {}): Promise<PaginatedResponse<Product>> => {
  const params = new URLSearchParams();
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== '') {
      params.append(key, String(value));
    }
  });

  const response = await api.get(`/products?${params}`);
  return {
    data: response.data.products || [],
    total: response.data.total || 0,
    page: filters.page || 1,
    limit: filters.limit || 20,
    total_pages: Math.ceil((response.data.total || 0) / (filters.limit || 20)),
  };
};

export const getProduct = async (id: string): Promise<Product> => {
  const response = await api.get(`/products/${id}`);
  return response.data;
};

export const getFeaturedProducts = async (limit: number = 8): Promise<Product[]> => {
  const response = await api.get(`/products?is_featured=true&limit=${limit}`);
  return response.data.products || [];
};

export const searchProducts = async (query: string, filters: FilterOptions = {}): Promise<PaginatedResponse<Product>> => {
  const params = new URLSearchParams();
  params.append('search', query);
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== '') {
      params.append(key, String(value));
    }
  });

  const response = await api.get(`/products?${params}`);
  return {
    data: response.data.products || [],
    total: response.data.total || 0,
    page: filters.page || 1,
    limit: filters.limit || 20,
    total_pages: Math.ceil((response.data.total || 0) / (filters.limit || 20)),
  };
};

// Orders API
export const createOrder = async (orderData: any): Promise<Order> => {
  const response = await api.post('/orders', orderData);
  return response.data;
};

export const getOrder = async (id: string): Promise<Order> => {
  const response = await api.get(`/orders/${id}`);
  return response.data;
};

export const getOrderByNumber = async (orderNumber: string): Promise<Order> => {
  const response = await api.get(`/orders/number/${orderNumber}`);
  return response.data;
};

// Customers API
export const createCustomer = async (customerData: CustomerFormData): Promise<Customer> => {
  const response = await api.post('/customers', customerData);
  return response.data;
};

export const getCustomer = async (id: string): Promise<Customer> => {
  const response = await api.get(`/customers/${id}`);
  return response.data;
};

export const getCustomerByPhone = async (phone: string): Promise<Customer> => {
  const response = await api.get(`/customers/phone/${phone}`);
  return response.data;
};

// Analytics API (for tracking collection views)
export const trackCollectionView = async (collectionId: string, sessionId?: string): Promise<void> => {
  await api.post('/analytics/view', {
    event_type: 'view',
    entity_type: 'collection',
    entity_id: collectionId,
    session_id: sessionId,
  });
};

export const trackProductView = async (productId: string, sessionId?: string): Promise<void> => {
  await api.post('/analytics/view', {
    event_type: 'view',
    entity_type: 'product',
    entity_id: productId,
    session_id: sessionId,
  });
};

// Collection Access API (for customer authentication)
export const getCollectionByShareURL = async (shareUrlId: string): Promise<any> => {
  const response = await api.get(`/collections/access/${shareUrlId}`);
  return response.data;
};

export const validateCollectionAccess = async (shareUrlId: string, accessToken: string): Promise<any> => {
  const response = await api.post(`/collections/access/${shareUrlId}/validate`, {
    access_token: accessToken
  });
  return response.data;
};

// Health check
export const healthCheck = async (): Promise<{ status: string; service: string; version: string }> => {
  const response = await api.get('/health');
  return response.data;
};

export default api;
