// Customer Frontend App - Simplified for Collection Access Only
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { CartProvider } from './contexts/CartContext';
import { NotificationProvider } from './contexts/NotificationContext';
import CollectionAccessPage from './pages/CollectionAccessPage';
import SimpleCollectionPage from './pages/SimpleCollectionPage';
import SimpleCartPage from './pages/SimpleCartPage';
import OrderSuccessPage from './pages/OrderSuccessPage';
import CartFooter from './components/common/CartFooter';
import ErrorBoundary from './components/common/ErrorBoundary';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

function App() {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <NotificationProvider>
          <CartProvider>
            <Router>
              <div className="min-h-screen bg-gray-50">
                <Routes>
                  {/* Collection Access Routes */}
                  <Route path="/collections/access/:shareUrlId" element={<CollectionAccessPage />} />
                  <Route path="/collection/:collectionId" element={<SimpleCollectionPage />} />

                  {/* Cart Routes */}
                  <Route path="/cart" element={<SimpleCartPage />} />
                  <Route path="/order-success" element={<OrderSuccessPage />} />

                  {/* Default route redirects to access page */}
                  <Route path="/" element={
                    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                      <div className="text-center">
                        <h1 className="text-2xl font-bold text-gray-900 mb-4">Collection Access Required</h1>
                        <p className="text-gray-600">Please use the collection access link provided to you.</p>
                      </div>
                    </div>
                  } />

                  {/* 404 Route */}
                  <Route path="*" element={
                    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                      <div className="text-center">
                        <h1 className="text-2xl font-bold text-gray-900 mb-4">Page Not Found</h1>
                        <p className="text-gray-600">The page you're looking for doesn't exist.</p>
                      </div>
                    </div>
                  } />
                </Routes>

                {/* Cart Footer - shows on all pages except cart page */}
                <CartFooter />
              </div>
            </Router>
          </CartProvider>
        </NotificationProvider>
        <ReactQueryDevtools initialIsOpen={false} />
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;
