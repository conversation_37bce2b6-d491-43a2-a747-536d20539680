import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { ShoppingBagIcon, ScaleIcon } from '@heroicons/react/24/outline';
import { useCart } from '../../contexts/CartContext';

const CartFooter: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { cart } = useCart();

  // Don't show footer if cart is empty or on cart/order pages
  if (cart.total_items === 0 || location.pathname === '/cart' || location.pathname === '/order-success') {
    return null;
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between py-3">
          {/* Cart Summary */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <ShoppingBagIcon className="h-5 w-5" />
              <span>{cart.total_items} item{cart.total_items !== 1 ? 's' : ''}</span>
            </div>
            <div className="flex items-center space-x-2 text-sm font-medium text-blue-600">
              <ScaleIcon className="h-5 w-5" />
              <span>{cart.total_weight.toFixed(1)}g total</span>
            </div>
          </div>

          {/* View Cart Button */}
          <button
            onClick={() => navigate('/cart')}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2 text-sm font-medium"
          >
            <span>View Cart</span>
            <ShoppingBagIcon className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default CartFooter;
