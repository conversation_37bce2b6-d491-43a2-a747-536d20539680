import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import { createProduct } from '../../lib/api';
import ProductForm from '../../components/products/ProductForm';
import ToastContainer from '../../components/common/ToastContainer';
import { useToast } from '../../hooks/useToast';
import type { CreateProductRequest } from '../../types';

const ProductCreatePage: React.FC = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { toasts, addToast, removeToast } = useToast();

  const createMutation = useMutation({
    mutationFn: createProduct,
    onSuccess: (data) => {
      // Invalidate products list to show the new product
      queryClient.invalidateQueries({ queryKey: ['products'] });

      // Show success toast
      addToast({
        message: `Product "${data.name}" created successfully!`,
        type: 'success',
      });

      // Navigate to the products list
      navigate('/products');
    },
    onError: (error: any) => {
      console.error('Failed to create product:', error);

      // Extract detailed error message
      let errorMessage = 'Failed to create product';
      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.response?.data?.details) {
        errorMessage = error.response.data.details;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      // Show error toast
      addToast({
        message: errorMessage,
        type: 'error',
        duration: 8000, // Show error longer
      });
    },
  });

  const handleSubmit = (data: CreateProductRequest) => {
    createMutation.mutate(data);
  };

  const handleCancel = () => {
    navigate('/products');
  };

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center space-x-4">
        <button
          onClick={() => navigate('/products')}
          className="text-gray-400 hover:text-gray-500"
        >
          <ArrowLeftIcon className="h-6 w-6" />
        </button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Create Product</h1>
          <p className="mt-1 text-sm text-gray-500">
            Add a new product to your jewelry catalog
          </p>
        </div>
      </div>

      {/* Product form */}
      <div className="card p-6">
        <ProductForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isLoading={createMutation.isPending}
          error={createMutation.error}
        />
      </div>

      {/* Toast notifications */}
      <ToastContainer toasts={toasts} onRemoveToast={removeToast} />
    </div>
  );
};

export default ProductCreatePage;
