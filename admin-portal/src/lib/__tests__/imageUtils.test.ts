import { describe, it, expect } from 'vitest';
import {
  getOptimizedImageUrl,
  getOptimizedProductImageUrl,
  getBestImageSize,
  getResponsiveImageUrls,
  generateSrcSet,
  generateSizesAttribute,
  getPlaceholderImageUrl,
  IMAGE_USE_CASES,
  IMAGE_DIMENSIONS,
} from '../imageUtils';
import type { ProductImage } from '../../types';

describe('imageUtils', () => {
  const mockProductImage: ProductImage = {
    id: 'test-image-id',
    product_id: 'test-product-id',
    image_url: 'http://localhost:8080/api/v1/proxy/images/product-id/image-id',
    alt_text: 'Test image',
    display_order: 1,
    is_primary: true,
    created_at: '2023-01-01T00:00:00Z',
  };

  const mockNonProxyUrl = 'https://example.com/image.jpg';

  describe('getOptimizedImageUrl', () => {
    it('should add size parameter to proxy URLs', () => {
      const result = getOptimizedImageUrl(mockProductImage.image_url, 'thumbnail');
      expect(result).toContain('size=thumbnail');
    });

    it('should add quality parameter when specified', () => {
      const result = getOptimizedImageUrl(mockProductImage.image_url, 'medium', 80);
      expect(result).toContain('size=medium');
      expect(result).toContain('quality=80');
    });

    it('should return non-proxy URLs unchanged', () => {
      const result = getOptimizedImageUrl(mockNonProxyUrl, 'large');
      expect(result).toBe(mockNonProxyUrl);
    });

    it('should return empty string for empty input', () => {
      const result = getOptimizedImageUrl('');
      expect(result).toBe('');
    });

    it('should default to medium size', () => {
      const result = getOptimizedImageUrl(mockProductImage.image_url);
      expect(result).toContain('size=medium');
    });
  });

  describe('getOptimizedProductImageUrl', () => {
    it('should optimize ProductImage objects', () => {
      const result = getOptimizedProductImageUrl(mockProductImage, 'small');
      expect(result).toContain('size=small');
    });

    it('should pass through quality parameter', () => {
      const result = getOptimizedProductImageUrl(mockProductImage, 'large', 90);
      expect(result).toContain('size=large');
      expect(result).toContain('quality=90');
    });
  });

  describe('getBestImageSize', () => {
    it('should return correct size for list thumbnails', () => {
      const result = getBestImageSize('LIST_THUMBNAIL');
      expect(result).toBe('thumbnail');
    });

    it('should return correct size for gallery main', () => {
      const result = getBestImageSize('GALLERY_MAIN');
      expect(result).toBe('large');
    });

    it('should return correct size for zoom view', () => {
      const result = getBestImageSize('ZOOM_VIEW');
      expect(result).toBe('original');
    });
  });

  describe('getResponsiveImageUrls', () => {
    it('should generate URLs for all sizes', () => {
      const result = getResponsiveImageUrls(mockProductImage.image_url);
      
      expect(result.thumbnail).toContain('size=thumbnail');
      expect(result.small).toContain('size=small');
      expect(result.medium).toContain('size=medium');
      expect(result.large).toContain('size=large');
      expect(result.original).toContain('size=original');
    });
  });

  describe('generateSrcSet', () => {
    it('should generate proper srcset string', () => {
      const result = generateSrcSet(mockProductImage.image_url);
      
      expect(result).toContain('300w');
      expect(result).toContain('600w');
      expect(result).toContain('1200w');
      expect(result.split(',').length).toBe(3);
    });
  });

  describe('generateSizesAttribute', () => {
    it('should generate default sizes attribute', () => {
      const result = generateSizesAttribute();
      
      expect(result).toContain('(max-width: 640px) 100vw');
      expect(result).toContain('(max-width: 1024px) 50vw');
      expect(result).toContain('33vw');
    });

    it('should use custom breakpoints', () => {
      const result = generateSizesAttribute({
        mobile: '90vw',
        tablet: '45vw',
        desktop: '30vw',
      });
      
      expect(result).toContain('90vw');
      expect(result).toContain('45vw');
      expect(result).toContain('30vw');
    });
  });

  describe('getPlaceholderImageUrl', () => {
    it('should generate placeholder URL with default dimensions', () => {
      const result = getPlaceholderImageUrl();
      expect(result).toContain('/images/placeholder');
      expect(result).toContain('width=300');
      expect(result).toContain('height=300');
    });

    it('should generate placeholder URL with custom dimensions', () => {
      const result = getPlaceholderImageUrl(400, 200);
      expect(result).toContain('width=400');
      expect(result).toContain('height=200');
    });
  });

  describe('IMAGE_USE_CASES', () => {
    it('should have all expected use cases', () => {
      expect(IMAGE_USE_CASES.LIST_THUMBNAIL).toBe('thumbnail');
      expect(IMAGE_USE_CASES.CARD_SMALL).toBe('small');
      expect(IMAGE_USE_CASES.CARD_MEDIUM).toBe('medium');
      expect(IMAGE_USE_CASES.FORM_PREVIEW).toBe('medium');
      expect(IMAGE_USE_CASES.GALLERY_MAIN).toBe('large');
      expect(IMAGE_USE_CASES.DETAIL_VIEW).toBe('large');
      expect(IMAGE_USE_CASES.ZOOM_VIEW).toBe('original');
      expect(IMAGE_USE_CASES.DOWNLOAD).toBe('original');
    });
  });

  describe('IMAGE_DIMENSIONS', () => {
    it('should have correct dimensions for each size', () => {
      expect(IMAGE_DIMENSIONS.thumbnail).toEqual({ width: 150, height: 150 });
      expect(IMAGE_DIMENSIONS.small).toEqual({ width: 300, height: 300 });
      expect(IMAGE_DIMENSIONS.medium).toEqual({ width: 600, height: 600 });
      expect(IMAGE_DIMENSIONS.large).toEqual({ width: 1200, height: 1200 });
      expect(IMAGE_DIMENSIONS.original).toEqual({ width: null, height: null });
    });
  });
});
