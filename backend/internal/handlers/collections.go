package handlers

import (
	"database/sql"
	"net/http"
	"strconv"

	"github.com/anandjewels/jewelry-backend/internal/database"
	"github.com/anandjewels/jewelry-backend/internal/middleware"
	"github.com/anandjewels/jewelry-backend/internal/models"
	"github.com/anandjewels/jewelry-backend/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// CollectionHandler handles collection-related HTTP requests
type CollectionHandler struct {
	db                *database.DB
	collectionService *services.CollectionService
}

// NewCollectionHandler creates a new collection handler
func NewCollectionHandler(db *database.DB) *CollectionHandler {
	return &CollectionHandler{
		db:                db,
		collectionService: services.NewCollectionService(db),
	}
}

// CreateCollection creates a new collection
// @Summary Create a new collection
// @Description Create a new jewelry collection
// @Tags collections
// @Accept json
// @Produce json
// @Param collection body models.CreateCollectionRequest true "Collection data"
// @Success 201 {object} models.CollectionResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/collections [post]
func (h *CollectionHandler) CreateCollection(c *gin.Context) {
	var req models.CreateCollectionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_request",
			Message: "Invalid request body",
			Code:    http.StatusBadRequest,
			Details: err.Error(),
		})
		return
	}

	collection, err := h.collectionService.CreateCollection(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "creation_failed",
			Message: "Failed to create collection",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, collection.ToResponse())
}

// GetCollections retrieves a list of collections with filtering and pagination
// @Summary Get collections
// @Description Get a list of collections with optional filtering and pagination
// @Tags collections
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param search query string false "Search in name and description"
// @Param is_active query bool false "Filter by active status"
// @Param is_public query bool false "Filter by public status"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/collections [get]
func (h *CollectionHandler) GetCollections(c *gin.Context) {
	// Parse query parameters
	req := &models.CollectionListRequest{
		Page:  1,
		Limit: 20,
	}

	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			req.Page = p
		}
	}

	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil && l > 0 && l <= 100 {
			req.Limit = l
		}
	}

	if search := c.Query("search"); search != "" {
		req.Search = &search
	}

	if isActive := c.Query("is_active"); isActive != "" {
		if active, err := strconv.ParseBool(isActive); err == nil {
			req.IsActive = &active
		}
	}

	if isPublic := c.Query("is_public"); isPublic != "" {
		if public, err := strconv.ParseBool(isPublic); err == nil {
			req.IsPublic = &public
		}
	}

	if sortBy := c.Query("sort_by"); sortBy != "" {
		req.SortBy = sortBy
	}

	if sortOrder := c.Query("sort_order"); sortOrder != "" {
		req.SortOrder = sortOrder
	}

	collections, total, err := h.collectionService.GetCollections(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "fetch_failed",
			Message: "Failed to fetch collections",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	// Convert to response format
	collectionResponses := make([]*models.CollectionResponse, len(collections))
	for i, collection := range collections {
		collectionResponses[i] = collection.ToResponse()
	}

	c.Header("X-Total-Count", strconv.Itoa(total))
	c.JSON(http.StatusOK, gin.H{
		"collections": collectionResponses,
		"total":       total,
		"page":        req.Page,
		"limit":       req.Limit,
	})
}

// GetCollection retrieves a single collection by ID
// @Summary Get collection by ID
// @Description Get a single collection by its ID
// @Tags collections
// @Accept json
// @Produce json
// @Param id path string true "Collection ID"
// @Success 200 {object} models.CollectionResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 404 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/collections/{id} [get]
func (h *CollectionHandler) GetCollection(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_id",
			Message: "Invalid collection ID format",
			Code:    http.StatusBadRequest,
		})
		return
	}

	collection, err := h.collectionService.GetCollectionWithProducts(id)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, middleware.ErrorResponse{
				Error:   "not_found",
				Message: "Collection not found",
				Code:    http.StatusNotFound,
			})
			return
		}
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "fetch_failed",
			Message: "Failed to fetch collection",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, collection.ToResponse())
}

// GetCollectionBySlug retrieves a single collection by slug (for public sharing)
// @Summary Get collection by slug
// @Description Get a single collection by its slug for public sharing
// @Tags collections
// @Accept json
// @Produce json
// @Param slug path string true "Collection Slug"
// @Success 200 {object} models.CollectionResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 404 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/collections/slug/{slug} [get]
func (h *CollectionHandler) GetCollectionBySlug(c *gin.Context) {
	slug := c.Param("slug")
	if slug == "" {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_slug",
			Message: "Collection slug is required",
			Code:    http.StatusBadRequest,
		})
		return
	}

	collection, err := h.collectionService.GetCollectionBySlug(slug)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, middleware.ErrorResponse{
				Error:   "not_found",
				Message: "Collection not found",
				Code:    http.StatusNotFound,
			})
			return
		}
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "fetch_failed",
			Message: "Failed to fetch collection",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	// Increment view count for public access
	if collection.IsPublic {
		_ = h.collectionService.IncrementViewCount(collection.ID)
	}

	c.JSON(http.StatusOK, collection.ToResponse())
}

// GetCollectionWithProducts retrieves a collection with its products
// @Summary Get collection with products
// @Description Get a collection with all its products
// @Tags collections
// @Accept json
// @Produce json
// @Param id path string true "Collection ID"
// @Success 200 {object} models.CollectionResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 404 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/collections/{id}/products [get]
func (h *CollectionHandler) GetCollectionWithProducts(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_id",
			Message: "Invalid collection ID format",
			Code:    http.StatusBadRequest,
		})
		return
	}

	collection, err := h.collectionService.GetCollectionWithProducts(id)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, middleware.ErrorResponse{
				Error:   "not_found",
				Message: "Collection not found",
				Code:    http.StatusNotFound,
			})
			return
		}
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "fetch_failed",
			Message: "Failed to fetch collection with products",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, collection.ToResponse())
}

// AddProductToCollection adds a product to a collection
// @Summary Add product to collection
// @Description Add a product to a collection
// @Tags collections
// @Accept json
// @Produce json
// @Param id path string true "Collection ID"
// @Param request body models.AddProductToCollectionRequest true "Product data"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 404 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/collections/{id}/products [post]
func (h *CollectionHandler) AddProductToCollection(c *gin.Context) {
	idStr := c.Param("id")
	collectionID, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_id",
			Message: "Invalid collection ID format",
			Code:    http.StatusBadRequest,
		})
		return
	}

	var req models.AddProductToCollectionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_request",
			Message: "Invalid request body",
			Code:    http.StatusBadRequest,
			Details: err.Error(),
		})
		return
	}

	err = h.collectionService.AddProductToCollection(collectionID, req.ProductID, req.DisplayOrder, req.IsFeatured)
	if err != nil {
		if err.Error() == "product already exists in collection" {
			c.JSON(http.StatusConflict, middleware.ErrorResponse{
				Error:   "product_exists",
				Message: "Product already exists in collection",
				Code:    http.StatusConflict,
			})
			return
		}
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "add_failed",
			Message: "Failed to add product to collection",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":       "Product added to collection successfully",
		"collection_id": collectionID,
		"product_id":    req.ProductID,
	})
}

// RemoveProductFromCollection removes a product from a collection
// @Summary Remove product from collection
// @Description Remove a product from a collection
// @Tags collections
// @Accept json
// @Produce json
// @Param id path string true "Collection ID"
// @Param product_id path string true "Product ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 404 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/collections/{id}/products/{product_id} [delete]
func (h *CollectionHandler) RemoveProductFromCollection(c *gin.Context) {
	idStr := c.Param("id")
	collectionID, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_id",
			Message: "Invalid collection ID format",
			Code:    http.StatusBadRequest,
		})
		return
	}

	productIDStr := c.Param("product_id")
	productID, err := uuid.Parse(productIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_product_id",
			Message: "Invalid product ID format",
			Code:    http.StatusBadRequest,
		})
		return
	}

	err = h.collectionService.RemoveProductFromCollection(collectionID, productID)
	if err != nil {
		if err.Error() == "product not found in collection" {
			c.JSON(http.StatusNotFound, middleware.ErrorResponse{
				Error:   "product_not_found",
				Message: "Product not found in collection",
				Code:    http.StatusNotFound,
			})
			return
		}
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "remove_failed",
			Message: "Failed to remove product from collection",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":       "Product removed from collection successfully",
		"collection_id": collectionID,
		"product_id":    productID,
	})
}

// UpdateCollection updates an existing collection
// @Summary Update collection
// @Description Update an existing collection by ID
// @Tags Collections
// @Accept json
// @Produce json
// @Param id path string true "Collection ID"
// @Param collection body models.CreateCollectionRequest true "Updated collection data"
// @Success 200 {object} models.CollectionResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 404 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/collections/{id} [put]
func (h *CollectionHandler) UpdateCollection(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_id",
			Message: "Invalid collection ID format",
			Code:    http.StatusBadRequest,
		})
		return
	}

	var req models.UpdateCollectionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_request",
			Message: "Invalid request body",
			Code:    http.StatusBadRequest,
			Details: err.Error(),
		})
		return
	}

	collection, err := h.collectionService.UpdateCollection(id, &req)
	if err != nil {
		if err.Error() == "collection not found" {
			c.JSON(http.StatusNotFound, middleware.ErrorResponse{
				Error:   "not_found",
				Message: "Collection not found",
				Code:    http.StatusNotFound,
			})
			return
		}

		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "update_failed",
			Message: "Failed to update collection",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, collection.ToResponse())
}

// DeleteCollection deletes a collection
// @Summary Delete collection
// @Description Delete a collection by ID (soft delete)
// @Tags Collections
// @Accept json
// @Produce json
// @Param id path string true "Collection ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 404 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/collections/{id} [delete]
func (h *CollectionHandler) DeleteCollection(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_id",
			Message: "Invalid collection ID format",
			Code:    http.StatusBadRequest,
		})
		return
	}

	err = h.collectionService.DeleteCollection(id)
	if err != nil {
		if err.Error() == "collection not found" {
			c.JSON(http.StatusNotFound, middleware.ErrorResponse{
				Error:   "not_found",
				Message: "Collection not found",
				Code:    http.StatusNotFound,
			})
			return
		}

		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "delete_failed",
			Message: "Failed to delete collection",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":       "Collection deleted successfully",
		"collection_id": id,
	})
}

// UpdateCollectionStatus updates the active status of a collection
// @Summary Update collection status
// @Description Update the active status of a collection by ID
// @Tags Collections
// @Accept json
// @Produce json
// @Param id path string true "Collection ID"
// @Param status body map[string]bool true "Status update data"
// @Success 200 {object} models.CollectionResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 404 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/collections/{id}/status [patch]
func (h *CollectionHandler) UpdateCollectionStatus(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_id",
			Message: "Invalid collection ID format",
			Code:    http.StatusBadRequest,
		})
		return
	}

	var req struct {
		IsActive bool `json:"is_active"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_request",
			Message: "Invalid request body",
			Code:    http.StatusBadRequest,
			Details: err.Error(),
		})
		return
	}

	collection, err := h.collectionService.UpdateCollectionStatus(id, req.IsActive)
	if err != nil {
		if err.Error() == "collection not found" {
			c.JSON(http.StatusNotFound, middleware.ErrorResponse{
				Error:   "not_found",
				Message: "Collection not found",
				Code:    http.StatusNotFound,
			})
			return
		}

		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "update_failed",
			Message: "Failed to update collection status",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, collection.ToResponse())
}
