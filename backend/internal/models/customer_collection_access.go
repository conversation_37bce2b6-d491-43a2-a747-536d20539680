package models

import (
	"time"

	"github.com/google/uuid"
)

// CustomerCollectionAccess represents customer-specific access to collections
type CustomerCollectionAccess struct {
	ID             uuid.UUID  `json:"id" db:"id"`
	CollectionID   uuid.UUID  `json:"collection_id" db:"collection_id"`
	CustomerID     uuid.UUID  `json:"customer_id" db:"customer_id"`
	ShareUrlID     string     `json:"share_url_id" db:"share_url_id"`
	AccessToken    string     `json:"access_token" db:"access_token"`
	CustomerPhone  string     `json:"customer_phone" db:"customer_phone"`
	ExpiresAt      *time.Time `json:"expires_at" db:"expires_at"`
	IsActive       bool       `json:"is_active" db:"is_active"`
	AccessCount    int        `json:"access_count" db:"access_count"`
	LastAccessedAt *time.Time `json:"last_accessed_at" db:"last_accessed_at"`
	CreatedAt      time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt      time.Time  `json:"updated_at" db:"updated_at"`

	// Related data (not stored in customer_collection_access table)
	Collection *Collection `json:"collection,omitempty"`
	Customer   *Customer   `json:"customer,omitempty"`
}

// CreateCustomerCollectionAccessRequest represents request to create customer collection access
type CreateCustomerCollectionAccessRequest struct {
	CollectionID  uuid.UUID  `json:"collection_id" validate:"required"`
	CustomerID    uuid.UUID  `json:"customer_id" validate:"required"`
	CustomerPhone string     `json:"customer_phone" validate:"required"`
	ExpiresAt     *time.Time `json:"expires_at"`
}

// CustomerCollectionAccessResponse represents customer collection access data for API responses
type CustomerCollectionAccessResponse struct {
	ID             uuid.UUID   `json:"id"`
	CollectionID   uuid.UUID   `json:"collection_id"`
	CustomerID     uuid.UUID   `json:"customer_id"`
	ShareUrlID     string      `json:"share_url_id"`
	AccessToken    string      `json:"access_token"`
	CustomerPhone  string      `json:"customer_phone"`
	ExpiresAt      *time.Time  `json:"expires_at"`
	IsActive       bool        `json:"is_active"`
	AccessCount    int         `json:"access_count"`
	LastAccessedAt *time.Time  `json:"last_accessed_at"`
	CreatedAt      time.Time   `json:"created_at"`
	UpdatedAt      time.Time   `json:"updated_at"`
	Collection     *Collection `json:"collection,omitempty"`
	Customer       *Customer   `json:"customer,omitempty"`
}

// ToResponse converts CustomerCollectionAccess to CustomerCollectionAccessResponse
func (cca *CustomerCollectionAccess) ToResponse() *CustomerCollectionAccessResponse {
	return &CustomerCollectionAccessResponse{
		ID:             cca.ID,
		CollectionID:   cca.CollectionID,
		CustomerID:     cca.CustomerID,
		ShareUrlID:     cca.ShareUrlID,
		AccessToken:    cca.AccessToken,
		CustomerPhone:  cca.CustomerPhone,
		ExpiresAt:      cca.ExpiresAt,
		IsActive:       cca.IsActive,
		AccessCount:    cca.AccessCount,
		LastAccessedAt: cca.LastAccessedAt,
		CreatedAt:      cca.CreatedAt,
		UpdatedAt:      cca.UpdatedAt,
		Collection:     cca.Collection,
		Customer:       cca.Customer,
	}
}

// IsExpired checks if the access token has expired
func (cca *CustomerCollectionAccess) IsExpired() bool {
	if cca.ExpiresAt == nil {
		return false
	}
	return time.Now().After(*cca.ExpiresAt)
}

// IsValid checks if the access token is valid (active and not expired)
func (cca *CustomerCollectionAccess) IsValid() bool {
	return cca.IsActive && !cca.IsExpired()
}

// GenerateShareURL generates a unique shareable URL for the collection
func (cca *CustomerCollectionAccess) GenerateShareURL(baseURL string) string {
	return baseURL + "/collections/access/" + cca.ID.String()
}
