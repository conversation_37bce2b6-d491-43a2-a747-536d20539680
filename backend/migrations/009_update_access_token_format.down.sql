-- Rollback migration: Revert access token format to original base64 style

-- Clear existing tokens since they're in the new format
DELETE FROM customer_collection_access;

-- Revert the column type
ALTER TABLE customer_collection_access 
    ALTER COLUMN access_token TYPE VARCHAR(255);

-- Revert the function to generate base64 tokens
CREATE OR REPLACE FUNCTION generate_access_token() RETURNS VARCHAR(255) AS $$
BEGIN
    RETURN encode(gen_random_bytes(32), 'base64url');
END;
$$ LANGUAGE plpgsql;
