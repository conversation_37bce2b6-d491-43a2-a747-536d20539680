-- Migration: Update access token format to 4-digit OTP style
-- This changes the access token from long base64 strings to simple 4-digit numbers

-- First, clear existing tokens since they're in the old format
DELETE FROM customer_collection_access;

-- Update the column type and constraint
ALTER TABLE customer_collection_access 
    ALTER COLUMN access_token TYPE VARCHAR(4);

-- Update the function to generate 4-digit OTP-style tokens
CREATE OR REPLACE FUNCTION generate_access_token() RETURNS VARCHAR(4) AS $$
BEGIN
    RETURN LPAD((FLOOR(RANDOM() * 10000))::TEXT, 4, '0');
END;
$$ LANGUAGE plpgsql;
