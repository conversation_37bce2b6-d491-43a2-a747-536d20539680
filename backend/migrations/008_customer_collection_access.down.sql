-- Rollback migration: Remove customer-specific collection access

-- Drop trigger
DROP TRIGGER IF EXISTS update_customer_collection_access_updated_at ON customer_collection_access;

-- Drop functions (only if they're not used elsewhere)
DROP FUNCTION IF EXISTS update_updated_at_column();
DROP FUNCTION IF EXISTS generate_access_token();

-- Drop indexes
DROP INDEX IF EXISTS idx_customer_collection_access_active;
DROP INDEX IF EXISTS idx_customer_collection_access_phone;
DROP INDEX IF EXISTS idx_customer_collection_access_customer_id;
DROP INDEX IF EXISTS idx_customer_collection_access_collection_id;
DROP INDEX IF EXISTS idx_customer_collection_access_token;

-- Drop table
DROP TABLE IF EXISTS customer_collection_access;
